#!/bin/bash
set -e

# Deployment Fix Verification Script
# This script helps verify that the GitLab Deploy Token and ECS deployment fixes are working correctly

echo "🔍 AWS ECS Deployment Fix Verification"
echo "======================================"

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "SUCCESS")
            echo -e "${GREEN}✅ $message${NC}"
            ;;
        "WARNING")
            echo -e "${YELLOW}⚠️  $message${NC}"
            ;;
        "ERROR")
            echo -e "${RED}❌ $message${NC}"
            ;;
        "INFO")
            echo -e "${BLUE}ℹ️  $message${NC}"
            ;;
    esac
}

# Check if required tools are installed
check_tools() {
    print_status "INFO" "Checking required tools..."
    
    if ! command -v aws &> /dev/null; then
        print_status "ERROR" "AWS CLI not found. Please install AWS CLI."
        exit 1
    fi
    
    if ! command -v terraform &> /dev/null; then
        print_status "ERROR" "Terraform not found. Please install Terraform."
        exit 1
    fi
    
    print_status "SUCCESS" "Required tools are installed"
}

# Check AWS credentials
check_aws_credentials() {
    print_status "INFO" "Checking AWS credentials..."
    
    if aws sts get-caller-identity &> /dev/null; then
        local account_id=$(aws sts get-caller-identity --query Account --output text)
        local user_arn=$(aws sts get-caller-identity --query Arn --output text)
        print_status "SUCCESS" "AWS credentials are valid"
        print_status "INFO" "Account ID: $account_id"
        print_status "INFO" "User/Role: $user_arn"
    else
        print_status "ERROR" "AWS credentials are not configured or invalid"
        print_status "INFO" "Please run: aws configure"
        exit 1
    fi
}

# Check if deploy token variables are set (for local testing)
check_deploy_token_vars() {
    print_status "INFO" "Checking deploy token environment variables..."
    
    if [ -z "$GITLAB_DEPLOY_TOKEN_USERNAME" ]; then
        print_status "WARNING" "GITLAB_DEPLOY_TOKEN_USERNAME not set (OK for GitLab CI)"
    else
        print_status "SUCCESS" "GITLAB_DEPLOY_TOKEN_USERNAME is set: $GITLAB_DEPLOY_TOKEN_USERNAME"
    fi
    
    if [ -z "$GITLAB_DEPLOY_TOKEN_PASSWORD" ]; then
        print_status "WARNING" "GITLAB_DEPLOY_TOKEN_PASSWORD not set (OK for GitLab CI)"
    else
        print_status "SUCCESS" "GITLAB_DEPLOY_TOKEN_PASSWORD is set: [REDACTED]"
    fi
}

# Check ECS cluster status
check_ecs_cluster() {
    local environment=${1:-development}
    local cluster_name="aws-ecs-demo-${environment}"
    
    print_status "INFO" "Checking ECS cluster: $cluster_name"
    
    if aws ecs describe-clusters --clusters "$cluster_name" --query 'clusters[0].status' --output text 2>/dev/null | grep -q "ACTIVE"; then
        print_status "SUCCESS" "ECS cluster $cluster_name is ACTIVE"
        
        # Check service status
        local service_name="aws-ecs-demo-${environment}"
        print_status "INFO" "Checking ECS service: $service_name"
        
        local service_status=$(aws ecs describe-services --cluster "$cluster_name" --services "$service_name" --query 'services[0].status' --output text 2>/dev/null || echo "NOT_FOUND")
        
        if [ "$service_status" = "ACTIVE" ]; then
            print_status "SUCCESS" "ECS service $service_name is ACTIVE"
            
            # Check running tasks
            local running_count=$(aws ecs describe-services --cluster "$cluster_name" --services "$service_name" --query 'services[0].runningCount' --output text 2>/dev/null || echo "0")
            local desired_count=$(aws ecs describe-services --cluster "$cluster_name" --services "$service_name" --query 'services[0].desiredCount' --output text 2>/dev/null || echo "0")
            
            print_status "INFO" "Running tasks: $running_count / $desired_count"
            
            if [ "$running_count" -eq "$desired_count" ] && [ "$running_count" -gt 0 ]; then
                print_status "SUCCESS" "All desired tasks are running"
            else
                print_status "WARNING" "Not all tasks are running. Check ECS console for details."
            fi
        else
            print_status "WARNING" "ECS service status: $service_status"
        fi
    else
        print_status "WARNING" "ECS cluster $cluster_name not found or not active"
    fi
}

# Check Secrets Manager for deploy token
check_secrets_manager() {
    local environment=${1:-development}
    local secret_name="aws-ecs-demo-${environment}-gitlab-deploy-token"
    
    print_status "INFO" "Checking Secrets Manager secret: $secret_name"
    
    if aws secretsmanager describe-secret --secret-id "$secret_name" &> /dev/null; then
        print_status "SUCCESS" "Deploy token secret exists in Secrets Manager"
        
        # Try to get the secret value (this will fail if permissions are wrong)
        if aws secretsmanager get-secret-value --secret-id "$secret_name" --query 'SecretString' --output text &> /dev/null; then
            print_status "SUCCESS" "Deploy token secret is accessible"
        else
            print_status "WARNING" "Deploy token secret exists but is not accessible (check IAM permissions)"
        fi
    else
        print_status "WARNING" "Deploy token secret not found in Secrets Manager"
        print_status "INFO" "This is expected if Terraform hasn't been applied yet"
    fi
}

# Check CloudWatch logs for errors
check_cloudwatch_logs() {
    local environment=${1:-development}
    local log_group="/ecs/aws-ecs-demo-${environment}"
    
    print_status "INFO" "Checking CloudWatch logs: $log_group"
    
    if aws logs describe-log-groups --log-group-name-prefix "$log_group" --query 'logGroups[0].logGroupName' --output text 2>/dev/null | grep -q "$log_group"; then
        print_status "SUCCESS" "CloudWatch log group exists"
        
        # Check for recent log streams
        local stream_count=$(aws logs describe-log-streams --log-group-name "$log_group" --order-by LastEventTime --descending --max-items 5 --query 'length(logStreams)' --output text 2>/dev/null || echo "0")
        
        if [ "$stream_count" -gt 0 ]; then
            print_status "SUCCESS" "Found $stream_count recent log streams"
            print_status "INFO" "Check CloudWatch console for detailed logs"
        else
            print_status "WARNING" "No recent log streams found"
        fi
    else
        print_status "WARNING" "CloudWatch log group not found"
        print_status "INFO" "This is expected if no tasks have run yet"
    fi
}

# Main verification function
main() {
    local environment=${1:-development}
    
    echo ""
    print_status "INFO" "Starting verification for environment: $environment"
    echo ""
    
    check_tools
    echo ""
    
    check_aws_credentials
    echo ""
    
    check_deploy_token_vars
    echo ""
    
    check_ecs_cluster "$environment"
    echo ""
    
    check_secrets_manager "$environment"
    echo ""
    
    check_cloudwatch_logs "$environment"
    echo ""
    
    print_status "INFO" "Verification complete!"
    echo ""
    print_status "INFO" "Next steps:"
    echo "  1. If deploy token secret is missing, run Terraform apply"
    echo "  2. If ECS service is not running, check CloudWatch logs"
    echo "  3. If tasks are failing, verify deploy token permissions"
    echo "  4. Check AWS ECS console for detailed service status"
}

# Parse command line arguments
if [ $# -eq 0 ]; then
    main "development"
elif [ "$1" = "development" ] || [ "$1" = "production" ]; then
    main "$1"
else
    echo "Usage: $0 [development|production]"
    echo "Default: development"
    exit 1
fi
