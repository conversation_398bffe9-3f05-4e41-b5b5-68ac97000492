# Project Configuration
variable "project_name" {
  description = "Name of the project"
  type        = string
  default     = "aws-ecs-demo"
}

variable "environment" {
  description = "Environment name (development, production)"
  type        = string
  validation {
    condition     = contains(["development", "production"], var.environment)
    error_message = "Environment must be either 'development' or 'production'."
  }
}

# AWS Configuration
variable "aws_region" {
  description = "AWS region"
  type        = string
  default     = "us-east-1"
}

# Container Configuration
variable "container_image" {
  description = "Docker container image URI"
  type        = string
}

variable "container_port" {
  description = "Port exposed by the container"
  type        = number
  default     = 3000
}

variable "container_cpu" {
  description = "CPU units for the container (1024 = 1 vCPU)"
  type        = number
  default     = 256
}

variable "container_memory" {
  description = "Memory for the container in MB"
  type        = number
  default     = 512
}

# ECS Configuration
variable "desired_count" {
  description = "Desired number of tasks"
  type        = number
  default     = 1
}

# GitLab Registry Authentication
# Use GitLab Deploy Tokens for long-lived authentication instead of CI tokens
variable "gitlab_deploy_token_username" {
  description = "GitLab deploy token username (long-lived, not CI token)"
  type        = string
}

variable "gitlab_deploy_token_password" {
  description = "GitLab deploy token password (long-lived, not CI token)"
  type        = string
  sensitive   = true
}

# Repository Information
variable "repository_url" {
  description = "URL of the Git repository (for tagging resources)"
  type        = string
  default     = ""
}
