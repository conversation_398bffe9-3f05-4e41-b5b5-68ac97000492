#!/usr/bin/env sh
set -e

# Terraform initialization script for GitLab CI/CD
# This script configures Terraform backend for GitLab state management
# Uses environment variables for authentication to avoid token persistence in plan files

ENVIRONMENT=${1:-development}
PROJECT_ID=${CI_PROJECT_ID}
CI_JOB_TOKEN=${CI_JOB_TOKEN}
GITLAB_URL=${CI_SERVER_URL}

if [ -z "$PROJECT_ID" ] || [ -z "$CI_JOB_TOKEN" ] || [ -z "$GITLAB_URL" ]; then
    echo "❌ Error: Required GitLab CI variables are missing"
    echo "   PROJECT_ID: $PROJECT_ID"
    echo "   CI_JOB_TOKEN: [${CI_JOB_TOKEN:+SET}${CI_JOB_TOKEN:-MISSING}]"
    echo "   GITLAB_URL: $GITLAB_URL"
    exit 1
fi

echo "🔧 Initializing Terraform for environment: $ENVIRONMENT"
echo "📍 Project ID: $PROJECT_ID"
echo "🌐 GitLab URL: $GITLAB_URL"
echo "🔑 Using CI_JOB_TOKEN for authentication"

# Navigate to environment directory
cd "terraform/environments/$ENVIRONMENT"

# Build backend addresses
BACKEND_ADDRESS="${GITLAB_URL}/api/v4/projects/${PROJECT_ID}/terraform/state/${ENVIRONMENT}"
LOCK_ADDRESS="${BACKEND_ADDRESS}/lock"

echo "🔧 Backend configuration:"
echo "   Address: $BACKEND_ADDRESS"
echo "   Lock Address: $LOCK_ADDRESS"
echo "   Username: ${TF_HTTP_USERNAME:-NOT_SET}"
echo "   Password: ${TF_HTTP_PASSWORD:+[REDACTED]}${TF_HTTP_PASSWORD:-NOT_SET}"

# Verify authentication environment variables are set
if [ -z "$TF_HTTP_USERNAME" ] || [ -z "$TF_HTTP_PASSWORD" ]; then
    echo "❌ Error: Terraform authentication environment variables not set"
    echo "   TF_HTTP_USERNAME: ${TF_HTTP_USERNAME:-MISSING}"
    echo "   TF_HTTP_PASSWORD: ${TF_HTTP_PASSWORD:+SET}${TF_HTTP_PASSWORD:-MISSING}"
    echo "   These should be exported in the calling GitLab CI job"
    exit 1
fi

# Initialize Terraform with GitLab backend
# Use -backend-config for addresses (non-sensitive) and environment variables for auth (sensitive)
# This prevents token embedding in plan files while providing required configuration
terraform init \
    -backend-config="address=${BACKEND_ADDRESS}" \
    -backend-config="lock_address=${LOCK_ADDRESS}" \
    -backend-config="unlock_address=${LOCK_ADDRESS}" \
    -backend-config="lock_method=POST" \
    -backend-config="unlock_method=DELETE" \
    -backend-config="retry_wait_min=5"

echo "✅ Terraform initialized successfully for $ENVIRONMENT environment"
