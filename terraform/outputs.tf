# ECS Outputs
output "ecs_cluster_name" {
  description = "Name of the ECS cluster"
  value       = aws_ecs_cluster.main.name
}

output "ecs_service_name" {
  description = "Name of the ECS service"
  value       = aws_ecs_service.main.name
}

output "task_definition_arn" {
  description = "ARN of the task definition"
  value       = aws_ecs_task_definition.main.arn
}

# Application URL (simplified - no load balancer)
output "application_url" {
  description = "Note: Application runs on ECS Fargate with public IP. Check ECS console for task public IP."
  value       = "Check ECS console for task public IP on port ${var.container_port}"
}

# Container Image
output "deployed_image" {
  description = "Container image that was deployed"
  value       = var.container_image
}

# Environment Info
output "environment" {
  description = "Environment name"
  value       = var.environment
}

output "project_name" {
  description = "Project name"
  value       = var.project_name
}

# GitLab Deploy Token Authentication
output "gitlab_deploy_token_secret_arn" {
  description = "ARN of the GitLab deploy token authentication secret"
  value       = aws_secretsmanager_secret.gitlab_registry.arn
}

# Legacy output for backward compatibility
output "gitlab_registry_secret_arn" {
  description = "ARN of the GitLab registry authentication secret (legacy name)"
  value       = aws_secretsmanager_secret.gitlab_registry.arn
}
