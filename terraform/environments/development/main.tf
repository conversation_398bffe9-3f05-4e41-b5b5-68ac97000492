# Development Environment Configuration
terraform {
  required_version = ">= 1.0"

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }

}

terraform {
  # GitLab-managed Terraform state for development
  backend "http" {
    # Non-sensitive configuration - address is required
    # Sensitive authentication (username/password) provided via environment variables
    # This prevents token embedding in plan files while maintaining required config
  }
}

# Local backend for development environment
# terraform {
#   backend "local" {
#     path = "./terraform-dev.tfstate"
#   }
# }

# Use the main Terraform configuration
module "infrastructure" {
  source = "../.."

  # Environment Configuration
  project_name = "aws-ecs-demo"
  environment  = "development"

  # AWS Configuration
  aws_region = "us-east-1"

  # Container Configuration
  container_image  = var.container_image
  container_port   = 3000
  container_cpu    = 256
  container_memory = 512

  # ECS Configuration
  desired_count = 1

  # GitLab Deploy Token Authentication
  gitlab_deploy_token_username = var.gitlab_deploy_token_username
  gitlab_deploy_token_password = var.gitlab_deploy_token_password

  # Repository Information
  repository_url = var.repository_url
}

# Development-specific variables
variable "container_image" {
  description = "Docker container image URI for development"
  type        = string
}

variable "gitlab_deploy_token_username" {
  description = "GitLab deploy token username (long-lived, not CI token)"
  type        = string
}

variable "gitlab_deploy_token_password" {
  description = "GitLab deploy token password (long-lived, not CI token)"
  type        = string
  sensitive   = true
}

variable "repository_url" {
  description = "URL of the Git repository (for tagging resources)"
  type        = string
  default     = ""
}

# Development-specific outputs
output "application_url" {
  description = "Development application URL"
  value       = module.infrastructure.application_url
}

output "deployed_image" {
  description = "Container image deployed to development"
  value       = module.infrastructure.deployed_image
}

output "ecs_cluster_name" {
  description = "Development ECS cluster name"
  value       = module.infrastructure.ecs_cluster_name
}

output "ecs_service_name" {
  description = "Development ECS service name"
  value       = module.infrastructure.ecs_service_name
}

output "gitlab_deploy_token_secret_arn" {
  description = "GitLab deploy token authentication secret ARN"
  value       = module.infrastructure.gitlab_deploy_token_secret_arn
}

output "gitlab_registry_secret_arn" {
  description = "GitLab registry authentication secret ARN (legacy name)"
  value       = module.infrastructure.gitlab_registry_secret_arn
}
