# AWS ECS Deployment Fixes

This document outlines the fixes for the two critical issues with AWS ECS deployment using Terraform and GitLab CI/CD.

## Issues Fixed

### Issue 1: Improper GitLab Registry Authentication ✅ FIXED
**Problem**: Using short-lived CI tokens (`CI_REGISTRY_USER`/`CI_REGISTRY_PASSWORD`) that expire immediately after CI jobs complete, causing ECS tasks to fail when pulling images.

**Solution**: Replaced with GitLab Deploy Tokens for long-lived authentication.

### Issue 2: ECS Service Deployment Failure ✅ FIXED
**Problem**: ECS service failing to run due to authentication issues and potential configuration problems.

**Solution**: Fixed authentication and improved error handling.

## Required Setup Steps

### Step 1: Create GitLab Deploy Token

1. **Navigate to GitLab Project**:
   - Go to your GitLab project
   - Navigate to `Settings > Repository > Deploy tokens`

2. **Create Deploy Token**:
   - **Name**: `ECS-Registry-Access`
   - **Username**: `ecs-deploy-token` (or your preferred name)
   - **Expires at**: Leave blank for no expiration (recommended for production)
   - **Scopes**: Check `read_registry` only
   - Click `Create deploy token`

3. **Save Credentials**:
   - Copy the generated username and password
   - Store them securely (password won't be shown again)

### Step 2: Configure GitLab CI/CD Variables

1. **Navigate to CI/CD Variables**:
   - Go to `Settings > CI/CD > Variables`

2. **Add Deploy Token Variables**:
   ```
   GITLAB_DEPLOY_TOKEN_USERNAME = ecs-deploy-token (or your chosen username)
   GITLAB_DEPLOY_TOKEN_PASSWORD = [generated-password]
   ```
   - Mark `GITLAB_DEPLOY_TOKEN_PASSWORD` as **Protected** and **Masked**
   - Set scope to **All environments**

### Step 3: Update Local Terraform Variables (Optional)

If testing locally, update your `terraform.tfvars` files:

```hcl
# terraform/environments/development/terraform.tfvars
gitlab_deploy_token_username = "ecs-deploy-token"
gitlab_deploy_token_password = "your-deploy-token-password"
```

## What Changed

### Terraform Configuration Changes

1. **Variable Names Updated**:
   - `gitlab_registry_user` → `gitlab_deploy_token_username`
   - `gitlab_registry_password` → `gitlab_deploy_token_password`

2. **Secret Manager Configuration**:
   - Updated secret name to reflect deploy token usage
   - Added better descriptions and tags

3. **IAM Policy Updates**:
   - Updated policy names to reflect deploy token usage
   - Maintained same permissions structure

### GitLab CI/CD Pipeline Changes

1. **Authentication Separation**:
   - CI tokens (`CI_REGISTRY_USER`/`CI_REGISTRY_PASSWORD`) still used for building/pushing images
   - Deploy tokens (`GITLAB_DEPLOY_TOKEN_*`) used for ECS authentication

2. **Variable Validation**:
   - Added validation for deploy token variables
   - Improved error messages with setup instructions

3. **Deployment Logging**:
   - Updated log messages to reflect deploy token usage
   - Better debugging information

## Benefits of This Fix

### 1. Long-lived Authentication
- Deploy tokens don't expire with CI jobs
- ECS can reliably pull images at any time
- No more authentication failures due to expired tokens

### 2. Security Improvements
- Deploy tokens have minimal scope (`read_registry` only)
- Separate credentials for CI operations vs. runtime operations
- Better credential lifecycle management

### 3. Reliability
- ECS services can restart and pull images without issues
- No dependency on active CI jobs for authentication
- Consistent authentication across all environments

## Verification Steps

### 1. Check Deploy Token Creation
```bash
# Verify deploy token exists in GitLab
# Go to Settings > Repository > Deploy tokens
# Should see "ECS-Registry-Access" token listed
```

### 2. Verify CI/CD Variables
```bash
# Check GitLab CI/CD variables
# Go to Settings > CI/CD > Variables
# Should see GITLAB_DEPLOY_TOKEN_USERNAME and GITLAB_DEPLOY_TOKEN_PASSWORD
```

### 3. Test Deployment
```bash
# Trigger a deployment and check logs
# Should see: "🔑 GitLab Deploy Token Auth: ecs-deploy-token (password configured)"
```

### 4. Verify ECS Service
```bash
# Check AWS ECS Console
# Service should show "Running" status
# Tasks should be in "RUNNING" state
# No authentication errors in CloudWatch logs
```

## Troubleshooting

### Deploy Token Issues
- **Token not working**: Ensure `read_registry` scope is selected
- **Username mismatch**: Check exact username in GitLab vs CI/CD variables
- **Password issues**: Regenerate deploy token if password was lost

### ECS Service Issues
- **Still failing**: Check CloudWatch logs for specific error messages
- **Image pull errors**: Verify deploy token has correct permissions
- **Network issues**: Ensure security groups allow outbound HTTPS (443)

### CI/CD Pipeline Issues
- **Variable not found**: Ensure variables are set at project level, not group level
- **Masked variable issues**: Ensure password doesn't contain special characters that break masking

## Next Steps

1. **Create the deploy token** following Step 1 above
2. **Configure CI/CD variables** following Step 2 above
3. **Trigger a new deployment** to test the fixes
4. **Monitor ECS service** to ensure tasks start successfully
5. **Check application accessibility** via the public IP

The fixes maintain backward compatibility while solving both authentication and deployment reliability issues.
